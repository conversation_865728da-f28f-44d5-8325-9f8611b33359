{
    "tasks": [
        {
            "type": "cppbuild",
            "label": "C/C++: g++.exe build project with <PERSON><PERSON>per2",
            "command": "D:\\Program Files\\mingw64\\bin\\g++.exe",
            "args": [
                "-fdiagnostics-color=always",
                "-g",
                "-std=c++17",
                // 1. Include paths
                "-I${workspaceFolder}/cpp_implementation/libs/clipper2/include",
                "-I${workspaceFolder}/cpp_implementation/include",
                // 2. Your project's .cpp files
                //    Ensure you have a main.cpp or similar file with the main() function.
                //    If your main function is in another file, replace main.cpp with that filename.
                //    If you don't have a main.cpp yet for testing, you'll need to create one.
                "${workspaceFolder}/cpp_implementation/test/main.cpp",
                "${workspaceFolder}/cpp_implementation/src/Map3D.cpp",
                "${workspaceFolder}/cpp_implementation/src/OccupancyMap.cpp",
                "${workspaceFolder}/cpp_implementation/src/JPS.cpp",
                // Future .cpp files from your project will be added here
                // 3. Clipper2's .cpp files
                // "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.core.cpp",
                "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.engine.cpp",
                "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.offset.cpp",
                "${workspaceFolder}/cpp_implementation/libs/clipper2/src/clipper.rectclip.cpp",
                // 4. Output executable
                "-o",
                "${workspaceFolder}/cpp_implementation/test/Output.exe"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$gcc"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "detail": "Builds the project including Clipper2 library."
        }
    ],
    "version": "2.0.0"
}