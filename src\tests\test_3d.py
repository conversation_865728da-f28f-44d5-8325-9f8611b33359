import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, "src"))

from src.config.settings import initialize_settings

# # Add parent directory to path so we can import modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# # Set environment variables for testing
# os.environ["KAFKA_BOOTSTRAP_SERVERS"] = "localhost:9092"
# os.environ["KAFKA_TOPIC_PREFIX"] = "test"
# os.environ["REDIS_HOST"] = "localhost"
# os.environ["REDIS_PORT"] = "6379"

# from src.core.map.map_handler_3d import Map3D
from src.core.pathfinding.high_level_policy_3d import CBS3D
from visualization_3d import visualize_solution, visualize_static_elements, Visualizer3D

# from src.core.map.occupancy_map import OccupancyMap
from src.config import settings  # Corrected import

from pathfinding_cpp import Map3D, create_grid_converter, OccupancyMap, JPS

# from src.core.node_3d import GridNode3D

import numpy as np
import random
import time
import threading
import matplotlib.pyplot as plt
from colorama import init, Fore, Style

# 初始化colorama
init()


def generate_random_position(
    map3d, used_positions, start_y=0, start_x=0, end_y=400, end_x=400
):
    """
    生成一个随机的可用位置，并确保从该位置到高度20之间没有障碍物
    """
    while True:
        pos = (
            random.randint(start_y, end_y - 1),
            random.randint(start_x, end_x - 1),
            # map3d.depth - 1,  # 固定高度为10
            7,
        )

        # 检查生成的位置是否可通行且未被使用
        if not map3d.traversable(*pos) or pos in used_positions:
            continue

        # 检查从当前位置到高度20的垂直空间是否有障碍物
        is_clear = True
        for z in range(pos[2], map3d.depth - 1):  # 从当前高度检查到20
            check_pos = (pos[0], pos[1], z)
            if not map3d.traversable(*check_pos):
                is_clear = False
                break

        if is_clear:
            return pos


class Agent:
    def __init__(self, start, goal, start_time, solution):
        self.start = start
        self.goal = goal
        self.start_time = start_time
        self.solution = solution


def clear_last_line(n=1):
    """清除控制台最后n行"""
    for _ in range(n):
        print("\033[F\033[K", end="")


def print_status(msg, color=Fore.WHITE, clear_lines=1):
    """打印带颜色的状态信息"""
    clear_last_line(clear_lines)
    print(f"{color}{msg}{Style.RESET_ALL}")


def print_menu():
    """打印菜单"""
    print(f"\n{Fore.CYAN}可用命令：{Style.RESET_ALL}")
    print("1 - 添加新的智能体")
    print("2 - 显示当前智能体状态")
    print("q - 退出系统")
    print("\n请输入命令：", end="", flush=True)


def input_thread(shared_data):
    """处理用户输入的线程"""
    while not shared_data["should_exit"]:
        print_menu()
        cmd = input().strip().lower()

        if cmd == "q":
            shared_data["should_exit"] = True
            break

        if cmd == "1":
            shared_data["add_agent"] = True
        elif cmd == "2":
            shared_data["show_status"] = True
        else:
            print(f"{Fore.RED}无效的命令！{Style.RESET_ALL}")


def run_realtime_system():
    """运行实时控制系统"""
    print(f"\n{Fore.GREEN}实时控制系统启动{Style.RESET_ALL}")

    # 创建地图和CBS实例
    # map3d = Map3D(10, 10, 20, fixed_obstacles=False)
    map3d = Map3D()

    # no_fly_zone = {"center": {"lat": 31.9544872, "lon": 117.3743634}, "radius": 500}
    # map3d.add_solid_cylindrical_no_fly_zone(
    #     no_fly_zone["center"]["lat"],
    #     no_fly_zone["center"]["lon"],
    #     no_fly_zone["radius"],
    #     "zone1",
    # )

    # 添加多个均匀分布的圆柱体禁飞区， 生成100-400范围的网格状禁飞区（步长50）
    # zone_num = 0
    # for x in range(100, 401, 50):
    #     for y in range(150, 451, 50):
    #         map3d.add_solid_cylindrical_no_fly_zone_grid(x, y, 10, f"zone{zone_num}")
    #         zone_num += 1

    # 添加多边形禁飞区
    # import numpy as np
    # polygon = np.array(
    #     [[0.0, 0.0], [0.0, 10.0], [10.0, 10.0], [10.0, 0.0]], dtype=np.float32
    # )
    # map3d.add_solid_polygon_no_fly_zone_grid(polygon, "zone2")

    # map3d.obstacle_manager.register_type("no_fly_zone1", "禁飞区1")
    # # map3d.add_solid_cylindrical_no_fly_zone_grid(5, 5, 2, "no_fly_zone1")
    # map3d.add_solid_cylindrical_no_fly_zone_grid(220, 279, 10, "no_fly_zone1")

    # map3d.remove_no_fly_zone("no_fly_zone1")

    # map3d.set_center_obstacle(size=25)
    # map3d = Map3D(fixed_obstacles=False)
    # map3d.random_set_obstacles(0.00007)
    # map3d.set_center_obstacle()
    cbs = CBS3D(map3d)

    # 记录所有已使用的位置和智能体
    used_positions = set()
    agents = []

    # 创建可视化器
    plt.ion()  # 启用交互模式
    visualizer = Visualizer3D(map3d)

    # 上次更新时间
    last_update = 0

    # 共享数据
    shared_data = {"should_exit": False, "add_agent": False, "show_status": False}

    # 创建并启动输入线程
    input_thread_handle = threading.Thread(target=input_thread, args=(shared_data,))
    input_thread_handle.daemon = True
    input_thread_handle.start()

    while not shared_data["should_exit"]:
        current_time = int(time.time())

        # 处理添加智能体的请求
        if shared_data["add_agent"]:
            print_status("正在规划路径...", Fore.YELLOW)

            # 生成新的随机起点和终点
            start = generate_random_position(map3d, used_positions)
            # start = (2, 2, 1)
            # start = (1, 1, 5)
            # start = map3d.grid_converter.geo_to_relative(31.9593851, 117.3751173, 50)
            used_positions.add(start)

            goal = generate_random_position(map3d, used_positions)
            # goal = (8, 8, 5)
            # goal = (450, 450, 5)
            # goal = map3d.grid_converter.geo_to_relative(31.9511315, 117.3737720, 50)
            used_positions.add(goal)

            print(f"\n添加第 {len(agents) + 1} 个智能体:")
            print("起始位置:", start)
            print("目标位置:", goal)
            print("开始时间:", current_time)

            # 为新智能体寻找路径
            solution, error = cbs.solve(
                [start],
                [goal],
                18,
                start_times=[current_time],
            )

            if solution:
                print_status("规划成功!", Fore.GREEN)
                # 创建新的智能体并添加到列表
                new_agent = Agent(start, goal, current_time, solution)
                agents.append(new_agent)

                # 准备可视化数据
                all_starts = []
                all_goals = []
                all_solutions = {}

                for i, agent in enumerate(agents):
                    all_starts.append(agent.start)
                    all_goals.append(agent.goal)
                    if agent.solution:
                        # 获取路径列表
                        path = list(agent.solution.values())[0]
                        all_solutions[str(i)] = path

                # 更新可视化
                visualizer.update_visualization(all_solutions, all_starts, all_goals)
            else:
                failure_msg = list(error.values())[0] if error else "未知原因"
                print_status(f"规划失败! 原因: {failure_msg}", Fore.RED)
                # 释放占用的位置
                used_positions.remove(start)
                used_positions.remove(goal)

            shared_data["add_agent"] = False
            time.sleep(0.3)  # 添加短暂延迟，让用户能看清状态

        # 处理显示状态的请求
        if shared_data["show_status"]:
            print(f"\n{Fore.CYAN}当前系统状态：{Style.RESET_ALL}")
            print(f"智能体数量：{len(agents)}")
            for i, agent in enumerate(agents):
                print(f"\n智能体 {i+1}:")
                print(f"  起始位置：{agent.start}")
                print(f"  目标位置：{agent.goal}")
                print(f"  开始时间：{agent.start_time}")
            shared_data["show_status"] = False

        # 每秒更新一次显示
        if current_time > last_update:
            last_update = current_time

            # 准备可视化数据
            all_starts = []
            all_goals = []
            all_solutions = {}

            for i, agent in enumerate(agents):
                all_starts.append(agent.start)
                all_goals.append(agent.goal)
                if agent.solution:
                    # 获取路径列表
                    path = list(agent.solution.values())[0]
                    all_solutions[str(i)] = path

            # 更新可视化
            if agents:  # 只在有智能体时更新
                visualizer.update_visualization(all_solutions, all_starts, all_goals)

        plt.pause(0.05)  # 减少GUI更新间隔，提高响应性

    print(f"\n{Fore.YELLOW}系统正在退出...{Style.RESET_ALL}")
    plt.close("all")  # 关闭所有图形窗口


def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    map_3d = Map3D()
    map_3d._load_weather()


def test_single_agent():
    """测试单智能体路径规划"""

    # from src.core.pathfinding.jps_v4 import OptimizedPathFinder

    # 初始化地图和规划器
    # map_handler = Map3D(100, 100, 100, fixed_obstacles=False)

    converter = create_grid_converter(
        lat_size=settings.settings.map.grid_size.lat,
        lon_size=settings.settings.map.grid_size.lon,
        alt_size=settings.settings.map.grid_size.alt,
        min_lat=settings.settings.map.min_coords.lat,
        min_lon=settings.settings.map.min_coords.lon,
        min_alt=settings.settings.map.min_coords.alt,
    )

    map_handler = Map3D(
        100,
        100,
        100,
        converter,
    )

    # 创建共享的占用图实例
    # occupancy_map = OccupancyMap(
    #     self.map.height,
    #     self.map.width,
    #     self.map.depth,
    #     time_buffer=settings.settings.pathplanning.time_buffer,  # 由环境变量控制的时间缓冲 # Corrected access
    # )
    # high_level = CBS3D(map_handler)
    # occupancy_map = OccupancyMap(
    #     (10, 10, 10),
    #     time_buffer=settings.pathplanning.time_buffer,  # 由环境变量控制的时间缓冲
    # )
    # path = [GridNode3D(0, 7, 5, 0), GridNode3D(5, 7, 5, 1), GridNode3D(9, 7, 5, 2)]

    # occupancy_map.add_path(path, "test_agent0")
    occupancy_map = None

    # optimized_planner = OptimizedPathFinder(map_handler)

    optimized_planner = JPS(
        map_data=map_handler,
        occupancy_map_data=occupancy_map,
        takeoff_speed=0.2,
        cruise_speed=0.7,
        landing_speed=0.2,
        max_steps=100,
        need_smooth=False,
        smoothness=3.0,
        heuristic_weight=1.0,
        jump_step_size=1,
    )

    # 设置起点和终点
    start = (99, 0, 0)  # 起点坐标
    goal = (1, 99, 0)  # 终点坐标
    min_height = 50  # 最小巡航高度

    # map_handler.obstacle_manager.register_type("no_fly_zone1", "禁飞区1")
    # map_handler.add_solid_cylindrical_no_fly_zone_grid(
    #     50, 50, 20, "no_fly_zone1", max_height=min_height + 2
    # )

    map_handler.add_solid_cylindrical_no_fly_zone_grid(
        50, 50, 20, "no_fly_zone1", {}, 2
    )
    # map_handler.add_solid_polygon_no_fly_zone_grid(
    #     np.array(
    #         [
    #             # (0, 0),
    #             # (7, 7),
    #             # (2, 7),
    #             (20, 20),
    #             (70, 40),
    #             (40, 70),
    #             (10, 50),
    #         ]
    #     ),
    #     "no_fly_zone1",
    #     max_height=min_height + 3,
    #     expand_distance_grids=5,
    # )
    # map_handler.add_solid_cylindrical_no_fly_zone_grid(220, 279, 10, "no_fly_zone1")

    # zone_num = 0
    # for x in range(0, 100, 40):
    #     for y in range(0, 100, 40):
    #         map_handler.obstacle_manager.register_type(
    #             f"zone{zone_num}", f"禁飞区{zone_num}"
    #         )
    #         map_handler.add_solid_cylindrical_no_fly_zone_grid(
    #             x, y, 5, f"zone{zone_num}"
    #         )
    #         zone_num += 1

    # print(f"禁飞区数量: {zone_num}")

    print(f"开始规划路径...")
    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小巡航高度: {min_height}")

    # 调用规划器
    # solution, error = high_level.solve([start], [goal], min_height)

    solution, error = optimized_planner.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id=f"test_agent",
        start_time=0,
        # occupancy_map=occupancy_map,
        # constraints=None,
    )

    if solution:
        print(f"\n{Fore.GREEN}规划成功!{Style.RESET_ALL}")
        # 可视化结果
        solutions = {"test_agent": solution}  # 将路径添加到解决方案字典
        visualize_static_elements(map_handler, solutions, [start], [goal])
        print("可视化完成")
    else:
        failure_msg = error if error else "未知原因"
        print(f"\n{Fore.RED}规划失败! 原因: {failure_msg}{Style.RESET_ALL}")


def test_multi_agent_sequential_performance(agent_count, repeat_times=5):
    """测试多智能体顺序路径规划的性能

    Args:
        agent_count: 智能体数量
        repeat_times: 重复测试次数，默认为5
    """
    import time

    # from high_level_policy_3d import CBS3D
    # from map_handler_3d import Map3D
    import numpy as np

    # 使用固定地图
    # map_3d = Map3D()  # 使用数据库中的固定障碍物
    map_3d = Map3D(fixed_obstacles=False)
    map_3d.random_set_obstacles(0.3)
    cbs = CBS3D(map_3d)  # 使用同一个CBS实例

    print("\n=== 多智能体顺序路径规划性能测试 ===")
    print(f"智能体数量: {agent_count}")
    print(f"重复次数: {repeat_times}")
    print(f"地图大小: {map_3d.height}x{map_3d.width}x{map_3d.depth}")
    print(f"障碍物数量: {np.sum(map_3d.cells == 1)}")
    print("\n格式: 平均耗时(秒), 成功率, 平均路径长度")

    total_time = 0
    success_count = 0
    total_path_length = 0

    for _ in range(repeat_times):
        # 为每个智能体生成起点和终点
        starts = []
        goals = []
        used_positions = set()

        for i in range(agent_count):
            # 生成起点
            # start = generate_random_position(map_3d, used_positions)
            start = (1, 7, 5)
            used_positions.add(start)
            starts.append(start)

            # 生成终点
            # goal = generate_random_position(map_3d, used_positions)
            goal = (9, 3, 5)
            used_positions.add(goal)
            goals.append(goal)

            # 测试规划时间
            start_time = time.time()
            solution = cbs.solve(
                [start],
                [goal],
                min_height=20,
                agent_ids=[str(i)],
            )  # 使用固定的最小巡航高度
            end_time = time.time()

            if solution[0]:  # solution[0]是路径字典，solution[1]是错误信息字典
                total_time += end_time - start_time
                success_count += 1
                # 计算平均路径长度
                total_path_length += sum(
                    len(path) for path in solution[0].values()
                ) / len(solution[0])

    # 计算统计数据
    avg_time = total_time / repeat_times if success_count > 0 else float("inf")
    success_rate = success_count / repeat_times
    avg_path_length = total_path_length / success_count if success_count > 0 else 0

    print(f"{avg_time:.3f}, {success_rate:.2f}, {avg_path_length:.1f}")

    return {
        "avg_time": avg_time,
        "success_rate": success_rate,
        "avg_path_length": avg_path_length,
    }


def run_performance_test():
    """运行路径规划器的性能测试

    对比优化版本（JPS）和原始A*版本在以下方面的性能：
    1. 路径规划成功率
    2. 计算时间
    3. 路径长度
    4. 整体加速比

    测试过程：
    1. 创建随机测试地图
    2. 生成随机的起终点对
    3. 分别使用两种算法进行路径规划
    4. 收集并对比性能指标
    """
    import time
    import numpy as np

    # from low_level_policy_3d import AStar3D
    # from src.core.pathfinding.jps import (
    #     OptimizedPathFinder as OptimizedPathFinderV1,
    # )
    # from src.core.pathfinding.astar_v2 import (
    #     OptimizedPathFinder as OptimizedPathFinderV1,
    # )

    # from src.core.pathfinding.jps_v2 import OptimizedPathFinder as OptimizedPathFinderV1

    from src.core.pathfinding.jps_v3 import OptimizedPathFinder as OptimizedPathFinderV1

    from src.core.pathfinding.jps_v4 import OptimizedPathFinder

    # from ..map.map_handler_3d import Map3D
    # from ...tests.test_3d import generate_random_position

    # import random

    # 创建测试地图（使用默认数据库配置）
    test_map = Map3D(5000, 5000, 50, fixed_obstacles=False)
    map_size = (test_map.height, test_map.width, test_map.depth)  # 492 x 600 x 30
    occupancy_map = OccupancyMap(
        (test_map.height, test_map.width, test_map.depth),
        time_buffer=settings.settings.pathplanning.time_buffer,  # Corrected access
    )

    start_y = 150
    start_x = 77

    # 添加一些禁飞区域
    zone_num = 0

    test_map.obstacle_manager.register_type(f"zone{zone_num}", f"禁飞区{zone_num}")
    test_map.add_solid_polygon_no_fly_zone_grid(
        np.array([(500, 0), (500, 1000), (1000, 1000), (1000, 500), (500, 500)]),
        f"zone{zone_num}",
        expand_distance_grids=5,  # 安全缓冲区距离（网格单位）
    )
    zone_num += 1

    for x in range(1000, 3000, 700):
        for y in range(1000, 3000, 700):
            test_map.obstacle_manager.register_type(
                f"zone{zone_num}", f"禁飞区{zone_num}"
            )
            test_map.add_solid_cylindrical_no_fly_zone_grid(
                x, y, 300, f"zone{zone_num}"
            )
            zone_num += 1

    print(f"禁飞区数量: {zone_num}")

    # test_map.add_solid_cylindrical_no_fly_zone_grid(220, 279, 105, "zone1")

    # 创建测试用例
    test_cases = []
    num_test_cases = 10  # 测试用例数量
    min_height = 30  # 设置最小巡航高度
    used_positions = set()  # 用于确保起终点不重复

    print("正在生成随机测试用例...")
    for i in range(num_test_cases):
        # 生成随机起点，确保可达且未被使用
        start = generate_random_position(test_map, used_positions, 0, 0, 500, 500)
        used_positions.add(start)

        # 生成随机终点，确保可达且未被使用
        goal = generate_random_position(
            test_map, used_positions, 3000, 3000, 3500, 3500
        )
        used_positions.add(goal)

        test_cases.append((start, goal))
        print(f"测试用例 {i+1}: 起点 {start} -> 终点 {goal}")

    # 创建两种路径规划器实例
    optimized_planner = OptimizedPathFinder(test_map)  # 优化版本V2（JPS）
    original_planner = OptimizedPathFinderV1(test_map)  # 优化版本V1（JPS）

    # 性能测试统计数据
    print("\n开始性能测试...")
    print(f"地图大小: {map_size}")
    print(f"测试用例数量: {num_test_cases}")
    print(f"障碍物数量: {zone_num}")
    print("\n" + "=" * 50)

    # 初始化性能统计变量
    optimized_times = []  # 优化版本的计算时间
    original_times = []  # 原始版本的计算时间
    path_lengths = {"optimized": [], "original": []}  # 路径长度统计
    success_count = {"optimized": 0, "original": 0}  # 成功次数统计

    # 执行测试用例
    for i, (start, goal) in enumerate(test_cases):
        print(f"\n测试用例 {i+1}:")
        print(f"起点: {start}")
        print(f"终点: {goal}")

        # 测试优化版本（JPS）
        start_time = time.time()
        opt_path, opt_error = optimized_planner.find_path(
            start=start,
            goal=goal,
            min_height=min_height,
            agent_id=f"test_agent_{i}",
            start_time=0,
            occupancy_map=occupancy_map,
            constraints=None,
        )
        opt_time = time.time() - start_time
        optimized_times.append(opt_time)

        # 记录优化版本结果
        if opt_path:
            success_count["optimized"] += 1
            path_lengths["optimized"].append(len(opt_path[1]))
            print(
                f"优化版本: 成功 - 路径长度: {len(opt_path[1])}, 用时: {opt_time:.6f}秒"
            )
        else:
            print(f"优化版本: 失败 - {opt_error}, 用时: {opt_time:.6f}秒")

        # 测试原始A*版本
        start_time = time.time()
        orig_path, orig_error = original_planner.find_path(
            start=start,
            goal=goal,
            min_height=min_height,
            agent_id="test_agent",
            start_time=0,
            occupancy_map=None,
            constraints=None,
        )
        orig_time = time.time() - start_time
        original_times.append(orig_time)

        # 记录原始版本结果
        if orig_path:
            success_count["original"] += 1
            path_lengths["original"].append(len(orig_path[1]))
            print(
                f"原始版本: 成功 - 路径长度: {len(orig_path[1])}, 用时: {orig_time:.4f}秒"
            )
        else:
            print(f"原始版本: 失败 - {orig_error}, 用时: {orig_time:.4f}秒")

    # 输出详细的性能统计结果
    print("\n" + "=" * 50)
    print("\n性能统计结果:")

    # 计算成功率
    print(f"优化版本成功率: {success_count['optimized']/num_test_cases*100:.1f}%")
    print(f"原始版本成功率: {success_count['original']/num_test_cases*100:.1f}%")

    # 计算时间统计
    if optimized_times:
        print(f"\n优化版本（JPS）时间统计:")
        print(f"平均用时: {sum(optimized_times)/len(optimized_times):.4f}秒")
        print(f"最短用时: {min(optimized_times):.4f}秒")
        print(f"最长用时: {max(optimized_times):.4f}秒")

    if original_times:
        print(f"\n原始版本（JPS_v1）时间统计:")
        print(f"平均用时: {sum(original_times)/len(original_times):.4f}秒")
        print(f"最短用时: {min(original_times):.4f}秒")
        print(f"最长用时: {max(original_times):.4f}秒")

    # 计算平均路径长度
    if path_lengths["optimized"] and path_lengths["original"]:
        print("\n路径长度统计:")
        print(
            f"优化版本平均路径长度: {sum(path_lengths['optimized'])/len(path_lengths['optimized']):.1f}"
        )
        print(
            f"原始版本平均路径长度: {sum(path_lengths['original'])/len(path_lengths['original']):.1f}"
        )

    # 计算整体加速比
    if optimized_times and original_times:
        total_opt_time = sum(optimized_times)
        if total_opt_time > 0:  # 避免除零错误
            speedup = sum(original_times) / total_opt_time
            print(f"\n整体加速比: {speedup:.2f}x")
            if speedup > 1:
                print("优化版本显著提升了性能！")
            else:
                print("优化效果不明显，可能需要进一步调优")
        else:
            print("\n无法计算加速比：优化版本总用时为0")

    # 可视化所有路径和禁飞区
    # print("\n正在可视化路径和禁飞区...")

    # 创建可视化器
    # visualizer = Visualizer3D(test_map)

    # 准备可视化数据
    all_starts = []
    all_goals = []
    all_solutions = {}

    # 收集所有成功的路径
    for i, (start, goal) in enumerate(test_cases):
        # 添加起点和终点
        all_starts.append(start)
        all_goals.append(goal)

        # 优化版本的路径
        agent_id = f"opt_{i}"
        opt_path, _ = optimized_planner.find_path(
            start=start,
            goal=goal,
            min_height=min_height,
            agent_id=agent_id,
            start_time=0,
            occupancy_map=occupancy_map,
            constraints=None,
        )

        if opt_path:
            # 将路径转换为可视化格式
            path_nodes = opt_path  # 获取路径点列表
            all_solutions[agent_id] = path_nodes

    # 确保交互模式开启
    # plt.ion()

    # 使用静态可视化函数显示禁飞区、航路和起飞点终点
    # visualize_static_elements(test_map, all_solutions, all_starts, all_goals)
    # print("可视化完成")


def main():
    initialize_settings()
    # 运行实时系统
    # run_realtime_system()
    test_single_agent()
    # run_performance_test()

    # 测试不同智能体数量
    # agent_counts = [10]
    # results = []

    # for agent_count in agent_counts:
    #     result = test_multi_agent_sequential_performance(agent_count, repeat_times=1)
    #     results.append((agent_count, result))

    # # 打印汇总结果
    # print("\n=== 测试结果汇总 ===")
    # print("智能体数量, 平均耗时(秒), 成功率, 平均路径长度")
    # for agent_count, result in results:
    #     print(f"{agent_count}, {result['avg_time']:.3f}, {result['success_rate']:.2f}, {result['avg_path_length']:.1f}")


if __name__ == "__main__":
    main()
